// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import anduin.fdb.record.FDBCommonDatabase
import anduin.fundsub.advisortag.{FundSubAdvisorTagOperations, FundSubAdvisorTagStoreProvider}
import anduin.fundsub.datalakeingestion.model.RemoveAdvisorTagParams
import zio.Task

object RemoveAdvisorTagHandler {

  def handle(params: RemoveAdvisorTagParams): Task[Unit] = {
    for {
      _ <- FDBCommonDatabase().write(FundSubAdvisorTagStoreProvider.Production) { store =>
        val operations = FundSubAdvisorTagOperations(store)
        for {
          existingTagOpt <- operations.getOpt(params.id)
          _ <- existingTagOpt match {
            case Some(existingTag) if !existingTag.isRemoved =>
              operations.markAsRemoved(params.id)
            case _ =>
              // Tag doesn't exist or is already removed, do nothing
              zio.ZIO.unit
          }
        } yield ()
      }
    } yield ()
  }

}
