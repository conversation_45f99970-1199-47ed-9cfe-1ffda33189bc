// Copyright (C) 2014-2025 Anduin Transactions Inc.

package anduin.fundsub.datalakeingestion.handler

import anduin.fdb.record.FDBCommonDatabase
import anduin.fundsub.advisortag.{FundSubAdvisorTagOperations, FundSubAdvisorTagStoreProvider}
import anduin.fundsub.datalakeingestion.model.UpdateOrAddAdvisorTagParams
import anduin.protobuf.fundsub.{AdvisorTagColor, FundSubAdvisorTag}
import zio.Task

object UpdateOrAddAdvisorTagHandler {

  def handle(params: UpdateOrAddAdvisorTagParams): Task[Unit] = {
    for {
      _ <- FDBCommonDatabase().write(FundSubAdvisorTagStoreProvider.Production) { store =>
        val operations = FundSubAdvisorTagOperations(store)
        for {
          existingTagOpt <- operations.getOpt(params.id)
          _ <- existingTagOpt match {
            case Some(existingTag) =>
              // Update existing tag
              operations.update(
                params.id,
                _.withName(params.name)
                  .withColor(AdvisorTagColor.Gray1) // Default color for now
              )
            case None =>
              // Create new tag
              val newTag = FundSubAdvisorTag(
                id = params.id,
                fundSubId = params.id.parent,
                name = params.name,
                creator = params.creator.map(_.id),
                createdAt = Some(params.createdAt),
                color = AdvisorTagColor.Gray1, // Default color for now
                isRemoved = false
              )
              operations.add(newTag)
          }
        } yield ()
      }
    } yield ()
  }

}
