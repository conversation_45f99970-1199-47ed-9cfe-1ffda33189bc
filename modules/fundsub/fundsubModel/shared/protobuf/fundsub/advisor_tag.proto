syntax = "proto3";

package anduin.protobuf.fundsub.tag.advisor;

import "scalapb/scalapb.proto";
import "date_time.proto";

option (scalapb.options) = {
  package_name: "anduin.protobuf.fundsub"
  single_file: true
  import: "anduin.id.fundsub.FundSubId"
  import: "anduin.id.fundsub.FundSubAdvisorTagId"
  import: "anduin.model.common.user.UserId"
};

message FundSubAdvisorTag {
  string id = 1 [(scalapb.field).type = "FundSubAdvisorTagId"];
  string fundSubId = 2 [(scalapb.field).type = "FundSubId"];
  string name = 3;
  string creator = 4 [(scalapb.field).type = "Option[UserId]"];
  InstantMessage createdAt = 5 [(scalapb.field).type = "java.time.Instant"];
  bool isRemoved = 6;
  AdvisorTagColor color = 7;
}

enum AdvisorTagColor {
  Gray1 = 0;
  Red = 1;
  Orange = 2;
  YellowOrange = 3;
  Yellow = 4;
  YellowGreen = 5;
  Green = 6;
  BlueGreen = 7;
  Aqua = 8;
  Blue = 9;
  Indigo = 10;
  Purple = 11;
  Magenta = 12;
  HotPink = 13;
  Pink = 14;
  Gray7 = 15;
}

message RecordTypeUnion {
  FundSubAdvisorTag _FundSubAdvisorTag = 1;
}
